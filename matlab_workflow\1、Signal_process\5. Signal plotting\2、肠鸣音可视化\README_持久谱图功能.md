# 持久谱图功能使用说明

## 概述

本文档说明了新添加的持久谱图（Persistence Spectrum）绘制功能的使用方法和技术细节。

## 功能特性

### 新增函数：`plotPersistenceSpectrum`

位置：`matlab_workflow\1、Signal_process\5. Signal plotting\0、function\plotPersistenceSpectrum.m`

#### 功能描述
- 计算并绘制信号的持久谱图
- 显示信号在不同频率上的功率分布概率
- 适用于分析信号的频域稳定性和变化特征
- 能够识别间歇性频率成分

#### 函数接口
```matlab
plotPersistenceSpectrum(signal, fs, position, rows, cols, index, title_text, varargin)
```

#### 输入参数
- `signal`: 输入信号数据（数值向量）
- `fs`: 采样频率（标量，单位：Hz，通常为2570Hz）
- `position`: 图形窗口位置（[left, bottom, width, height]）
- `rows`: 子图行数（正整数）
- `cols`: 子图列数（正整数）
- `index`: 当前子图索引（正整数）
- `title_text`: 图形标题（字符串）
- `time_range`: 可选，时间轴范围（[t_min, t_max]，单位：秒）
- `freq_range`: 可选，频率轴范围（[f_min, f_max]，单位：Hz）
- `overlap_percent`: 可选，重叠百分比（0-99，默认50）

#### 技术参数
- 默认频率范围：[0, 1285] Hz（适合肠鸣音分析）
- 默认重叠百分比：50%
- 泄漏参数：0.85（平滑谱图）
- Y轴范围：[-120, -35] dB
- 字体设置：Times New Roman, 加粗, 16pt刻度, 18pt标签

## 修改的文件

### 1. `photograph.m` 主程序
位置：`matlab_workflow\1、Signal_process\5. Signal plotting\2、肠鸣音可视化\photograph.m`

#### 主要修改
- 更新了输出图表描述，从4种增加到5种可视化图表
- 添加了持久谱图函数调用
- 修改了子图布局为3行2列，以容纳5个图表
- 更新了依赖函数列表

#### 新的绘图调用
```matlab
% 绘制持久谱图
plotPersistenceSpectrum(signal, fs, position, 3, 2, 4, 'Persistence Spectrum of Mic (Body)', time_range, [0, 1285], 50);
```

### 2. `plotPersistenceSpectrum.m` 新函数
位置：`matlab_workflow\1、Signal_process\5. Signal plotting\0、function\plotPersistenceSpectrum.m`

#### 主要特性
- 完整的参数验证和错误处理
- 与现有绘图函数一致的接口设计
- 支持时间范围和频率范围自定义
- 支持重叠百分比调整
- 专业的科研级别图形格式

## 使用方法

### 基本使用
1. 运行 `photograph.m` 脚本
2. 选择包含timetable数据的.mat文件
3. 程序将自动生成5个可视化图表，包括新的持久谱图

### 单独调用持久谱图函数
```matlab
% 基本调用
plotPersistenceSpectrum(signal, 2570, [100,100,800,600], 1, 1, 1, 'Persistence Spectrum');

% 指定时间范围
plotPersistenceSpectrum(signal, 2570, [100,100,800,600], 1, 1, 1, 'Persistence Spectrum', [0, 60]);

% 指定时间和频率范围
plotPersistenceSpectrum(signal, 2570, [100,100,800,600], 1, 1, 1, 'Persistence Spectrum', [0, 60], [0, 1000]);

% 指定所有参数
plotPersistenceSpectrum(signal, 2570, [100,100,800,600], 1, 1, 1, 'Persistence Spectrum', [0, 60], [0, 1000], 75);
```

## 测试

### 测试脚本
位置：`matlab_workflow\1、Signal_process\5. Signal plotting\2、肠鸣音可视化\test_persistence_spectrum.m`

#### 测试内容
1. 基本功能测试 - 使用合成信号
2. 参数验证测试 - 测试错误处理
3. 接口一致性测试 - 与其他绘图函数对比
4. 子图布局测试 - 多子图环境测试

#### 运行测试
```matlab
% 在MATLAB命令窗口中运行
cd('matlab_workflow\1、Signal_process\5. Signal plotting\2、肠鸣音可视化')
test_persistence_spectrum
```

## 持久谱图的优势

### 相比传统功率谱的优势
1. **概率分布显示**：显示各频率-功率组合出现的概率
2. **稳定性分析**：能够分析信号频域特征的稳定性
3. **间歇性检测**：识别间歇性或瞬态频率成分
4. **噪声区分**：更好地区分信号和噪声成分

### 在肠鸣音分析中的应用
1. **频域稳定性评估**：评估肠鸣音信号的频域稳定性
2. **异常检测**：识别异常的频率模式
3. **信号质量评估**：评估录音质量和信号完整性
4. **特征提取**：为机器学习算法提供更丰富的特征

## 注意事项

1. **信号长度**：需要足够长的信号以获得有意义的持久谱
2. **采样频率**：确保采样频率满足奈奎斯特定理
3. **参数调整**：根据具体应用调整重叠百分比和频率范围
4. **内存使用**：长信号可能需要较多内存进行计算

## 错误处理

函数包含完整的错误处理机制：
- 输入参数类型和维度验证
- 采样频率有效性检查
- 时间和频率范围合理性验证
- 重叠百分比范围检查
- timetable创建错误处理
- pspectrum计算错误处理

## 技术支持

如有问题或建议，请联系医学信号处理团队。

---
*文档版本：1.0*  
*创建日期：2025-08-26*  
*最后更新：2025-08-26*
