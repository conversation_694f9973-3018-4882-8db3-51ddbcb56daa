%% 持久谱图调试脚本
% 用于调试持久谱图显示空白的问题

clear; close all; clc;

% 添加函数路径
addpath('../0、function');

%% 生成测试信号
fs = 2570; % 采样频率
duration = 10; % 信号长度（秒）
t = (0:1/fs:duration-1/fs)';
N = length(t);

% 创建包含多个频率成分的测试信号
f1 = 100; % 基频
f2 = 300; % 第二频率
f3 = 500; % 第三频率

% 合成信号：包含不同强度的频率成分和噪声
signal = 0.5 * sin(2*pi*f1*t) + ...
         0.3 * sin(2*pi*f2*t) + ...
         0.2 * sin(2*pi*f3*t) + ...
         0.1 * randn(N, 1); % 添加噪声

fprintf('信号长度: %d 样本\n', length(signal));
fprintf('信号时长: %.2f 秒\n', duration);
fprintf('采样频率: %d Hz\n', fs);

%% 测试1: 直接使用pspectrum函数（参考chijiuputu.m的方法）
fprintf('\n=== 测试1: 直接使用pspectrum ===\n');

% 创建timetable
tt_test = timetable(seconds(t), signal, 'VariableNames', {'data'});

% 设置参数
timeLimits = seconds([0, duration]);
frequencyLimits = [0, 1285];
overlapPercent = 50;

% 选择时间区域
tt_ROI = tt_test(timerange(timeLimits(1), timeLimits(2), 'closed'), :);

try
    figure('Position', [100, 100, 800, 600]);
    pspectrum(tt_ROI, ...
        'persistence', ...
        'FrequencyLimits', frequencyLimits, ...
        'OverlapPercent', overlapPercent, ...
        'Leakage', 0.85);
    title('直接调用pspectrum - 持久谱图');
    fprintf('✓ 直接调用pspectrum成功\n');
catch ME
    fprintf('✗ 直接调用pspectrum失败: %s\n', ME.message);
end

%% 测试2: 使用我们的函数
fprintf('\n=== 测试2: 使用plot_PersistenceSpectrum函数 ===\n');

try
    position = [200, 200, 800, 600];
    plot_PersistenceSpectrum(signal, fs, position, 1, 1, 1, 'plot_PersistenceSpectrum测试');
    fprintf('✓ plot_PersistenceSpectrum函数调用成功\n');
catch ME
    fprintf('✗ plot_PersistenceSpectrum函数调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
end

%% 测试3: 使用函数带参数
fprintf('\n=== 测试3: 使用函数带时间和频率范围参数 ===\n');

try
    position = [300, 300, 800, 600];
    time_range = [2, 8]; % 2-8秒
    freq_range = [0, 1000]; % 0-1000Hz
    overlap_percent = 75; % 75%重叠
    
    plot_PersistenceSpectrum(signal, fs, position, 1, 1, 1, ...
        'plot_PersistenceSpectrum带参数测试', ...
        time_range, freq_range, overlap_percent);
    fprintf('✓ 带参数的plot_PersistenceSpectrum函数调用成功\n');
catch ME
    fprintf('✗ 带参数的plot_PersistenceSpectrum函数调用失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
end

%% 测试4: 检查信号特性
fprintf('\n=== 测试4: 信号特性检查 ===\n');

fprintf('信号统计信息:\n');
fprintf('  最小值: %.6f\n', min(signal));
fprintf('  最大值: %.6f\n', max(signal));
fprintf('  均值: %.6f\n', mean(signal));
fprintf('  标准差: %.6f\n', std(signal));
fprintf('  信号功率: %.6f\n', mean(signal.^2));

% 检查信号是否包含有效的频率成分
[pxx, f] = pwelch(signal, [], [], [], fs);
[~, max_idx] = max(pxx);
dominant_freq = f(max_idx);
fprintf('  主导频率: %.1f Hz\n', dominant_freq);

%% 测试5: 子图环境测试
fprintf('\n=== 测试5: 子图环境测试 ===\n');

try
    figure('Position', [400, 400, 1200, 800]);
    
    % 子图1: 时域波形
    subplot(2, 2, 1);
    plot(t, signal);
    title('时域波形');
    xlabel('时间 (s)');
    ylabel('幅值');
    
    % 子图2: 功率谱
    subplot(2, 2, 2);
    [pxx, f] = pwelch(signal, [], [], [], fs);
    plot(f, 10*log10(pxx));
    title('功率谱');
    xlabel('频率 (Hz)');
    ylabel('功率 (dB)');
    
    % 子图3: 频谱图
    subplot(2, 2, 3);
    spectrogram(signal, hamming(256), 230, 512, fs, 'yaxis');
    title('频谱图');
    
    % 子图4: 持久谱图
    subplot(2, 2, 4);
    % 直接在当前子图中调用pspectrum
    tt_test = timetable(seconds(t), signal, 'VariableNames', {'data'});
    pspectrum(tt_test, 'persistence', 'FrequencyLimits', [0, 1285], 'OverlapPercent', 50);
    title('持久谱图 (子图环境)');
    
    fprintf('✓ 子图环境测试成功\n');
catch ME
    fprintf('✗ 子图环境测试失败: %s\n', ME.message);
    fprintf('错误详情: %s\n', ME.getReport());
end

fprintf('\n=== 调试完成 ===\n');
fprintf('请检查生成的图形，看哪个方法能正确显示持久谱图。\n');
