function plot_PersistenceSpectrum(signal, fs, position, rows, cols, index, title_text, varargin)
%PLOT_PERSISTENCE_SPECTRUM 肠鸣音信号持久谱图绘制函数
%   计算并绘制信号的持久谱图，专门用于肠鸣音音频信号的频域分析。
%   持久谱图显示信号在不同频率上的功率分布概率，适用于分析信号的
%   频域稳定性和变化特征。该函数提供专业的科研级别可视化。
%
%   语法:
%   plot_persistence_spectrum(signal, fs, position, rows, cols, index, title_text)
%   plot_persistence_spectrum(signal, fs, position, rows, cols, index, title_text, time_range)
%   plot_persistence_spectrum(signal, fs, position, rows, cols, index, title_text, time_range, freq_range)
%   plot_persistence_spectrum(signal, fs, position, rows, cols, index, title_text, time_range, freq_range, overlap_percent)
%
%   输入参数:
%   signal         - 输入信号数据 (数值向量)
%   fs             - 采样频率 (标量，单位：Hz，通常为2570Hz)
%   position       - 图形窗口位置 ([left, bottom, width, height])
%   rows           - 子图行数 (正整数)
%   cols           - 子图列数 (正整数)
%   index          - 当前子图索引 (正整数)
%   title_text     - 图形标题 (字符串)
%   time_range     - 可选，时间轴范围 ([t_min, t_max]，单位：秒)
%   freq_range     - 可选，频率轴范围 ([f_min, f_max]，单位：Hz)
%   overlap_percent- 可选，重叠百分比 (0-99，默认50)
%
%   输出参数:
%   无 - 直接生成持久谱图显示
%
%   技术参数:
%   - 默认频率范围: [0, 1285] Hz (适合肠鸣音分析)
%   - 默认时间范围: 整个信号长度
%   - 默认重叠百分比: 50%
%   - 泄漏参数: 0.85 (平滑谱图)
%   - Y轴范围: [-120, -35] dB
%   - 字体设置: Times New Roman, 加粗, 16pt刻度, 18pt标签
%
%   图形特征:
%   - X轴: 频率 (Hz)
%   - Y轴: 功率谱密度 (dB)
%   - 颜色映射: 持久性概率分布
%   - 网格: 开启，便于读数
%
%   持久谱图特性:
%   - 显示信号在各频率上的功率分布概率
%   - 颜色深浅表示该频率-功率组合出现的概率
%   - 适用于分析信号的频域稳定性
%   - 能够识别间歇性频率成分
%
%   应用场景:
%   - 肠鸣音信号频域稳定性分析
%   - 间歇性频率成分识别
%   - 信号质量评估
%   - 频域特征的统计分析
%
%   使用示例:
%   % 基本调用
%   plot_persistence_spectrum(signal, 2570, [100,100,800,600], 1, 1, 1, 'Persistence Spectrum');
%
%   % 指定时间范围
%   plot_persistence_spectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Persistence Spectrum', [0, 60]);
%
%   % 指定时间和频率范围
%   plot_persistence_spectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Persistence Spectrum', [0, 60], [0, 1000]);
%
%   % 指定所有参数
%   plot_persistence_spectrum(signal, 2570, [100,100,800,600], 2, 1, 1, 'Persistence Spectrum', [0, 60], [0, 1000], 75);
%
%   注意事项:
%   - 输入信号应为一维数值向量
%   - 采样频率必须为正数
%   - 时间范围和频率范围应为递增的二元素向量
%   - 重叠百分比应在0-99之间
%   - 需要足够长的信号以获得有意义的持久谱
%
%   错误处理:
%   - 输入参数类型和维度验证
%   - 采样频率有效性检查
%   - 时间和频率范围合理性验证
%   - 重叠百分比范围检查
%
%   参见: PSPECTRUM, PLOT_WAVEFORM, PLOT_SPECTROGRAM, TIMETABLE
%
%   作者: [医学信号处理团队]
%   创建日期: 2025-08-26
%   最后修改: 2025-08-26
%   版本: 2.0

    % 输入参数验证
    if nargin < 7
        error('plot_persistence_spectrum:NotEnoughInputs', '至少需要7个输入参数');
    end

    % 验证信号数据
    if ~isnumeric(signal) || ~isvector(signal)
        error('plot_persistence_spectrum:InvalidSignal', '信号数据必须是数值向量');
    end

    % 验证采样频率
    if ~isnumeric(fs) || ~isscalar(fs) || fs <= 0
        error('plot_persistence_spectrum:InvalidSamplingRate', '采样频率必须是正数标量');
    end

    % 验证位置参数
    if ~isnumeric(position) || length(position) ~= 4
        error('plot_persistence_spectrum:InvalidPosition', '位置参数必须是4元素数值向量');
    end

    % 验证子图参数
    if ~isnumeric(rows) || ~isscalar(rows) || rows <= 0 || rows ~= round(rows)
        error('plot_persistence_spectrum:InvalidRows', '行数必须是正整数');
    end
    if ~isnumeric(cols) || ~isscalar(cols) || cols <= 0 || cols ~= round(cols)
        error('plot_persistence_spectrum:InvalidCols', '列数必须是正整数');
    end
    if ~isnumeric(index) || ~isscalar(index) || index <= 0 || index ~= round(index)
        error('plot_persistence_spectrum:InvalidIndex', '索引必须是正整数');
    end

    % 验证标题
    if ~ischar(title_text) && ~isstring(title_text)
        error('plot_persistence_spectrum:InvalidTitle', '标题必须是字符串');
    end

    % 处理可选参数
    time_range = [];
    freq_range = [0, 1285]; % 默认频率范围，适合肠鸣音分析
    overlap_percent = 50; % 默认重叠百分比

    if nargin >= 8 && ~isempty(varargin{1})
        time_range = varargin{1};
        if ~isnumeric(time_range) || length(time_range) ~= 2 || time_range(1) >= time_range(2)
            error('plot_persistence_spectrum:InvalidTimeRange', '时间范围必须是递增的二元素向量');
        end
    end

    if nargin >= 9 && ~isempty(varargin{2})
        freq_range = varargin{2};
        if ~isnumeric(freq_range) || length(freq_range) ~= 2 || freq_range(1) >= freq_range(2)
            error('plot_persistence_spectrum:InvalidFreqRange', '频率范围必须是递增的二元素向量');
        end
    end

    if nargin >= 10 && ~isempty(varargin{3})
        overlap_percent = varargin{3};
        if ~isnumeric(overlap_percent) || ~isscalar(overlap_percent) || overlap_percent < 0 || overlap_percent >= 100
            error('plot_persistence_spectrum:InvalidOverlap', '重叠百分比必须在0-99之间');
        end
    end

    % 确保信号为列向量
    signal = signal(:);

    % 计算信号时间向量
    N = length(signal);
    t = (0:N-1) / fs;

    % 处理时间范围
    if ~isempty(time_range)
        % 找到时间范围内的索引
        start_idx = max(1, round(time_range(1) * fs) + 1);
        end_idx = min(N, round(time_range(2) * fs) + 1);

        if start_idx >= end_idx
            error('plot_persistence_spectrum:InvalidTimeRange', '指定的时间范围超出信号长度');
        end

        % 截取信号
        signal = signal(start_idx:end_idx);
        t = t(start_idx:end_idx);
    end

    % 将信号转换为timetable格式（pspectrum持久谱需要）
    try
        tt = timetable(seconds(t'), signal, 'VariableNames', {'data'});
    catch ME
        error('plotPersistenceSpectrum:TimetableError', '创建timetable失败: %s', ME.message);
    end

    % 创建图形
    figure('Position', position);
    subplot(rows, cols, index);

    % 计算并绘制持久谱图
    try
        % 使用pspectrum函数计算持久谱，不需要返回值因为它直接绘图
        pspectrum(tt, ...
            'persistence', ...
            'FrequencyLimits', freq_range, ...
            'OverlapPercent', overlap_percent, ...
            'Leakage', 0.85); % 增加泄漏参数以平滑谱图
    catch ME
        error('plotPersistenceSpectrum:PspectrumError', '持久谱计算失败: %s', ME.message);
    end

    % 设置图形属性
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = 'bold';

    % 设置坐标轴范围
    xlim(freq_range);
    ylim([-120, -35]); % 适合肠鸣音信号的功率范围

    % 设置标签和标题
    xlabel('Frequency (Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Power Spectral Density (dB)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');

    % 开启网格
    grid on;
    ax.GridAlpha = 0.3;

    % 刷新图形窗口
    drawnow;

end