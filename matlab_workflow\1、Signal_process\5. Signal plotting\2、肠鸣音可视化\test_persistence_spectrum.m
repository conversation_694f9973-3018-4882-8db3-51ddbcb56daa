%% 持久谱图函数测试脚本
%TEST_PERSISTENCE_SPECTRUM 测试新创建的持久谱图绘制函数
%   该脚本用于验证plotPersistenceSpectrum函数的功能和接口设计
%
%   测试内容:
%   1. 基本功能测试 - 使用合成信号
%   2. 参数验证测试 - 测试错误处理
%   3. 接口一致性测试 - 与其他绘图函数对比
%
%   作者: [医学信号处理团队]
%   创建日期: 2025-08-26
%   版本: 1.0

% 清空工作空间
clear; close all; clc;

% 添加函数路径
addpath('../0、function');

%% 测试1: 基本功能测试
fprintf('=== 测试1: 基本功能测试 ===\n');

% 生成测试信号
fs = 2570; % 采样频率
duration = 10; % 信号长度（秒）
t = (0:1/fs:duration-1/fs)';
N = length(t);

% 创建包含多个频率成分的测试信号
f1 = 100; % 基频
f2 = 300; % 第二频率
f3 = 500; % 第三频率

% 合成信号：包含不同强度的频率成分和噪声
signal = 0.5 * sin(2*pi*f1*t) + ...
         0.3 * sin(2*pi*f2*t) + ...
         0.2 * sin(2*pi*f3*t) + ...
         0.1 * randn(N, 1); % 添加噪声

% 测试基本调用
try
    position = [100, 100, 1200, 800];
    plotPersistenceSpectrum(signal, fs, position, 1, 1, 1, 'Test Persistence Spectrum');
    fprintf('✓ 基本功能测试通过\n');
catch ME
    fprintf('✗ 基本功能测试失败: %s\n', ME.message);
end

%% 测试2: 参数测试
fprintf('\n=== 测试2: 参数测试 ===\n');

% 测试时间范围参数
try
    position = [200, 200, 1200, 800];
    time_range = [2, 8]; % 测试2-8秒的时间范围
    plotPersistenceSpectrum(signal, fs, position, 1, 1, 1, 'Test with Time Range', time_range);
    fprintf('✓ 时间范围参数测试通过\n');
catch ME
    fprintf('✗ 时间范围参数测试失败: %s\n', ME.message);
end

% 测试频率范围参数
try
    position = [300, 300, 1200, 800];
    time_range = [0, duration];
    freq_range = [0, 800]; % 测试0-800Hz的频率范围
    plotPersistenceSpectrum(signal, fs, position, 1, 1, 1, 'Test with Freq Range', time_range, freq_range);
    fprintf('✓ 频率范围参数测试通过\n');
catch ME
    fprintf('✗ 频率范围参数测试失败: %s\n', ME.message);
end

% 测试重叠百分比参数
try
    position = [400, 400, 1200, 800];
    time_range = [0, duration];
    freq_range = [0, 1000];
    overlap_percent = 75; % 测试75%重叠
    plotPersistenceSpectrum(signal, fs, position, 1, 1, 1, 'Test with Overlap', time_range, freq_range, overlap_percent);
    fprintf('✓ 重叠百分比参数测试通过\n');
catch ME
    fprintf('✗ 重叠百分比参数测试失败: %s\n', ME.message);
end

%% 测试3: 错误处理测试
fprintf('\n=== 测试3: 错误处理测试 ===\n');

% 测试无效信号输入
try
    plotPersistenceSpectrum('invalid', fs, position, 1, 1, 1, 'Test');
    fprintf('✗ 信号验证测试失败 - 应该抛出错误\n');
catch ME
    fprintf('✓ 信号验证测试通过: %s\n', ME.message);
end

% 测试无效采样频率
try
    plotPersistenceSpectrum(signal, -1, position, 1, 1, 1, 'Test');
    fprintf('✗ 采样频率验证测试失败 - 应该抛出错误\n');
catch ME
    fprintf('✓ 采样频率验证测试通过: %s\n', ME.message);
end

% 测试无效时间范围
try
    plotPersistenceSpectrum(signal, fs, position, 1, 1, 1, 'Test', [5, 2]); % 错误的时间范围
    fprintf('✗ 时间范围验证测试失败 - 应该抛出错误\n');
catch ME
    fprintf('✓ 时间范围验证测试通过: %s\n', ME.message);
end

%% 测试4: 子图布局测试
fprintf('\n=== 测试4: 子图布局测试 ===\n');

try
    % 创建2x2子图布局，测试持久谱图在多子图环境中的表现
    position = [500, 500, 1500, 1000];
    
    % 子图1: 波形图
    plot_waveform(seconds(t), signal, position, 2, 2, 1, 'Waveform', [0, duration]);
    
    % 子图2: 频谱图
    plot_spectrogram(signal, fs, position, 2, 2, 2, 'Spectrogram', [0, duration]);
    
    % 子图3: 功率谱图
    plot_pspectrum(signal, fs, position, 2, 2, 3, 'Power Spectrum', [0, duration], [0, 1000]);
    
    % 子图4: 持久谱图
    plotPersistenceSpectrum(signal, fs, position, 2, 2, 4, 'Persistence Spectrum', [0, duration], [0, 1000], 50);
    
    fprintf('✓ 子图布局测试通过\n');
catch ME
    fprintf('✗ 子图布局测试失败: %s\n', ME.message);
end

%% 测试总结
fprintf('\n=== 测试完成 ===\n');
fprintf('持久谱图函数 plotPersistenceSpectrum 测试完成。\n');
fprintf('请检查生成的图形是否符合预期。\n');
fprintf('如果所有测试都通过，函数可以正常使用。\n');
